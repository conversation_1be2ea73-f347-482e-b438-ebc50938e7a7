    @media (max-width: 768px) {
        .grid {
            flex-direction: column;
            display: inherit;
        }

        aside {
            width: 100% !important;
            display: inherit;
        }

        main {
            width: 100% !important;
            display: inherit;
        }
    }

    .h-90vh {
        height: 90vh;
        overflow-y: scroll;
        overflow-x: hidden;
    }

    .overflow-y-auto {
        max-height: 90vh;
        overflow-y: scroll;
        overflow-x: hidden;
    }

    .no-gap {
        gap: 0;
    }

    .modern-background {}

    .modern-card {
        background-color: #fff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        padding: 16px;
    }

    .modern-avatar {
        background-color: #3498db;
    }

    .modern-time {
        color: #7f8c8d;
    }

    .modern-unread {
        background-color: #e74c3c;
    }

    .modern-list-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
    }

    .header-icons i {
        margin-right: 15px;
        cursor: pointer;
        background-color: transparent;
        /* Remove background */
    }

    .header-icons i:hover {
        color: #3498db;
    }

    .emoji-picker {
        position: absolute;
        bottom: 60px;
        right: 20px;
        width: 300px;
        height: 300px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        overflow-y: scroll;
        overflow-x: hidden;
        display: none;
        padding: 10px;
        z-index: 10;
    }

    .emoji-picker.show {
        display: block;
    }

    .emoji-categories {
        display: flex;
        justify-content: space-around;
        margin-bottom: 10px;
    }

    .emoji-categories button {
        background: none;
        border: none;
        cursor: pointer;
        padding: 5px;
    }

    .emoji-categories button.active {
        border-bottom: 2px solid #3498db;
    }

    .emoji-picker ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .emoji-picker ul li {
        display: inline-block;
        padding: 5px;
        cursor: pointer;
        font-size: 20px;
    }

    .emoji-picker ul li:hover {
        background: #f0f4f8;
        border-radius: 4px;
    }

    .attachment-card {
        position: absolute;
        bottom: 80px;
        right: 20px;
        width: 200px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        overflow-x: hidden;
        display: none;
        padding: 10px;
        z-index: 10;
    }

    .attachment-card.show {
        display: block;
    }

    .attachment-card ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .attachment-card ul li {
        display: flex;
        align-items: center;
        padding: 10px;
        cursor: pointer;
        font-size: 16px;
    }

    .attachment-card ul li:hover {
        background: #f0f4f8;
        border-radius: 4px;
    }

    .attachment-card ul li i {
        margin-right: 10px;
    }

    .chat-background {
        background-image: url('assets/images/bg.jpg');
        background-size: cover;
        background-position: center;
    }

    .max-w-[300px] {
        max-width: 100%;
    }

    .max-h-[300px] {
        max-height: 100%;
    }

    .max-w-[60%] {
        max-width: 60%;
    }

    .bg-whatsapp-sent {
        background-color: #DCF8C6;
        box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    }

    .bg-whatsapp-received {
        background-color: #FFFFFF;
        box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    }

    .flex-row-reverse .bg-whatsapp-sent {
        margin-left: auto;
    }

    .flex-row-reverse .bg-whatsapp-received {
        margin-left: auto;
    }


    /* Icon Styling */
    .icon {
        color: #128C7E;
        font-size: 20px;
    }

    .icon:hover {
        color: #075E54;
    }

    /* Input Styling */
    input[type="text"] {
        border-radius: 20px;
        border: 1px solid #ddd;
        padding: 10px;
        width: 100%;
    }

    input[type="text"]:focus {
        border-color: #128C7E;
        box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
    }

    .avatar-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #3498db;
        color: #fff;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .chat-enter-active,
    .chat-leave-active {
        transition: opacity 0.5s;
    }

    .chat-enter,
    .chat-leave-to

    /* .chat-leave-active in <2.1.8 */
        {
        opacity: 0;
    }

    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.5s;
    }

    .fade-enter,
    .fade-leave-to

    /* .fade-leave-active in <2.1.8 */
        {
        opacity: 0;
    }

    .order-card {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .order-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .order-card-header h3 {
        margin: 0;
        color: #1f2937;
        font-size: 1.125rem;
        font-weight: 600;
    }

    .order-card-header span {
        color: #6b7280;
        font-size: 0.875rem;
    }

    .order-card-content {
        color: #374151;
        font-size: 0.875rem;
    }

    .order-card-content strong {
        color: #1f2937;
    }

    .order-card-content .border-b {
        border-bottom: 1px solid #e5e7eb;
    }

    .loader {
        border-radius: 50%;
        width: 24px;
        height: 24px;
        border: 4px solid rgba(200, 200, 200, 0.4);
        border-top-color: #3498db;
        animation: spin 0.6s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .animate-pulse {
        animation: pulse 1.5s ease-in-out infinite;
    }

    @keyframes pulse {

        0%,
        100% {
            opacity: 1;
        }

        50% {
            opacity: 0.5;
        }
    }
