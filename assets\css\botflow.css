body {
    display: flex;
    font-family: Arial, sans-serif;
    margin: 0;
    height: 100vh;
}

.sidebar {
    width: 200px;
    background: #f4f4f4;
    padding: 10px;
    display: flex;
    flex-direction: column;
}

.component {
    background: #007bff;
    color: white;
    padding: 10px;
    margin: 5px;
    cursor: grab;
    text-align: center;
    border-radius: 5px;
}

#builder {
    flex-grow: 1;
    position: relative;
    background: #eee;
    overflow: hidden;
}

.node {
    position: absolute;
    width: 150px;
    background: white;
    border: 2px solid #007bff;
    padding: 10px;
    cursor: pointer;
    text-align: center;
    border-radius: 5px;
}

#edit-sidebar {
    width: 250px;
    background: white;
    display: none;
    flex-direction: column;
    padding: 10px;
    position: absolute;
    top: 20px;
    right: 20px;
    border: 1px solid #ddd;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}

#arrow-container {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.animated-dotted-line {
    stroke: #007bff;
    stroke-width: 2;
    stroke-dasharray: 4;
    fill: none;
}

.save-button {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 20px;
    background: #28a745;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
}
.node {
    position: absolute;
    width: 200px;
    padding: 10px;
    cursor: pointer;
    text-align: center;
    border-radius: 8px;
    transition: transform 0.2s ease-in-out;
}

.node:hover {
    transform: scale(1.05);
}

.animated-dotted-line {
    stroke: #007bff;
    stroke-width: 2;
    stroke-dasharray: 5;
    fill: none;
}

.save-button {
    transition: all 0.2s;
}

.save-button:hover {
    transform: scale(1.1);
}
