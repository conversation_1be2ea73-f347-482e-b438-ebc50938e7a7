body, html {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    height: 100%;
    overflow-x: hidden;
}

header.header {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.docs-branding {
    padding: 15px;
}

.docs-logo-wrapper .site-logo img {
    width: 48px;
}

.docs-top-utilities {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.social-list a.btn {
    margin-right: 15px;
}

.docs-wrapper {
    padding-top: 90px; /* Adjust based on the header height */
    display: flex;
}

.docs-sidebar {
    width: 250px;
    background: #f8f9fa;
    padding: 20px;
}

.docs-sidebar nav ul {
    list-style: none;
    padding-left: 0;
}

.docs-sidebar nav ul li {
    margin-bottom: 15px;
}

.docs-content {
    flex: 1;
    padding: 20px;
}

.docs-content img {
    max-width: 100%;
}

.docs-section {
    margin-bottom: 40px;
}

.docs-footer {
    text-align: center;
    padding: 20px 0;
    background: #f8f9fa;
    font-size: 14px;
}

.container {
    max-width: 1140px;
    margin: auto;
}

h1.docs-heading {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.section-heading {
    font-size: 1.75rem;
    margin-bottom: 20px;
}

ol {
    list-style-position: inside;
    padding-left: 0;
}

ol li {
    margin-bottom: 10px;
}

.footer small {
    font-size: 14px;
}

.relative img {
    max-width: 100%;
}

.relative {
    overflow: hidden;
    position: relative;
    border-radius: 4px;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #004085;
    color: #fff;
}

.btn {
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    border-radius: 4px;
}
